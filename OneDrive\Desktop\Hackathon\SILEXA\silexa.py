import cv2
import numpy as np
import time

# Load Haar cascades for face and mouth
face_cascade = cv2.CascadeClassifier(cv2.data.haarcascades + 'haarcascade_frontalface_default.xml')
mouth_cascade = cv2.CascadeClassifier(cv2.data.haarcascades + 'haarcascade_mcs_mouth.xml')

cap = cv2.VideoCapture(0)

prev_frame = None
speech_started = False
start_time = 0

while True:
    ret, frame = cap.read()
    frame = cv2.flip(frame, 1)
    gray = cv2.cvtColor(frame, cv2.COLOR_BGR2GRAY)

    faces = face_cascade.detectMultiScale(gray, 1.3, 5)

    for (x, y, w, h) in faces:
        roi_gray = gray[y:y+h, x:x+w]
        mouths = mouth_cascade.detectMultiScale(roi_gray, 1.5, 11)

        for (mx, my, mw, mh) in mouths:
            if my > h // 2:  # Ignore false positives from eyes/nose
                mouth_img = roi_gray[my:my+mh, mx:mx+mw]
                mouth_img = cv2.resize(mouth_img, (100, 50))

                if prev_frame is not None:
                    diff = cv2.absdiff(prev_frame, mouth_img)
                    motion_score = np.sum(diff)

                    if motion_score > 30000 and not speech_started:
                        speech_started = True
                        start_time = time.time()
                        print("🟢 Speaking detected...")

                    elif motion_score < 10000 and speech_started:
                        duration = time.time() - start_time
                        speech_started = False

                        if 1 <= duration < 2:
                            print("Predicted: Yes")
                        elif 2 <= duration < 3:
                            print("Predicted: No")
                        elif 3 <= duration < 4:
                            print("Predicted: Hello")
                        elif 4 <= duration < 5:
                            print("Predicted: Help")
                        elif 5 <= duration < 6:
                            print("Predicted: Go")
                        elif duration >= 6:
                            print("Predicted: Stop")
                        else:
                            print("Predicted: [Unclear]")

                prev_frame = mouth_img.copy()
                cv2.rectangle(frame, (x+mx, y+my), (x+mx+mw, y+my+mh), (0, 255, 0), 2)
                break

    cv2.putText(frame, 'Speak Silently...', (10, 40),
                cv2.FONT_HERSHEY_SIMPLEX, 1, (255, 255, 255), 2)

    cv2.imshow('Lip Sync to Text (Simulated)', frame)

    if cv2.waitKey(1) & 0xFF == 27:  # Press ESC to exit
        break

cap.release()
cv2.destroyAllWindows()
