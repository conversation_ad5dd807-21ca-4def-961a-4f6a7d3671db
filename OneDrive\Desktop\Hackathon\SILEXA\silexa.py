import cv2
import numpy as np
import time
import sys

def initialize_cascades():
    """Initialize and validate cascade classifiers"""
    # Load cascades
    face_cascade = cv2.CascadeClassifier(cv2.data.haarcascades + 'haarcascade_frontalface_default.xml')
    # ✅ Use smile cascade (mouth alternative)
    mouth_cascade = cv2.CascadeClassifier(cv2.data.haarcascades + 'haarcascade_smile.xml')

    # Check if loaded correctly
    if face_cascade.empty():
        print("❌ Face cascade failed to load.")
        return None, None

    if mouth_cascade.empty():
        print("❌ Mouth cascade failed to load.")
        return None, None

    print("✅ Face cascade loaded.")
    print("✅ Mouth cascade loaded.")
    return face_cascade, mouth_cascade

def initialize_camera():
    """Initialize camera with error handling"""
    cap = cv2.VideoCapture(0)

    if not cap.isOpened():
        print("❌ Error: Could not open camera.")
        return None

    # Test if camera is working
    ret, frame = cap.read()
    if not ret:
        print("❌ Error: Could not read from camera.")
        cap.release()
        return None

    print("✅ Camera initialized successfully.")
    return cap

# Initialize components
face_cascade, mouth_cascade = initialize_cascades()
if face_cascade is None or mouth_cascade is None:
    print("❌ Failed to initialize cascades. Exiting.")
    sys.exit(1)

cap = initialize_camera()
if cap is None:
    print("❌ Failed to initialize camera. Exiting.")
    sys.exit(1)

def detect_speech_motion(prev_frame, current_frame):
    """Detect motion in mouth region with adaptive thresholds"""
    if prev_frame is None or current_frame is None:
        return 0

    # Calculate motion score
    diff = cv2.absdiff(prev_frame, current_frame)
    motion_score = np.sum(diff)

    # Normalize by frame size to make thresholds more consistent
    normalized_score = motion_score / (current_frame.shape[0] * current_frame.shape[1])

    return normalized_score

def predict_word(duration):
    """Predict word based on speech duration"""
    if duration < 0.5:
        return "[Too Short]"
    elif 0.5 <= duration < 2:
        return "Yes"
    elif 2 <= duration < 3:
        return "No"
    elif 3 <= duration < 4:
        return "Hello"
    elif 4 <= duration < 5:
        return "Help"
    elif 5 <= duration < 6:
        return "Go"
    elif duration >= 6:
        return "Stop"
    else:
        return "[Unclear]"

# Main processing variables
prev_frame = None
speech_started = False
start_time = 0
motion_threshold_high = 50  # Adaptive threshold for motion start
motion_threshold_low = 20   # Adaptive threshold for motion end
frame_count = 0

print("🎥 Starting lip reading detection...")
print("📝 Instructions:")
print("   - Look directly at the camera")
print("   - Speak silently (mouth movements only)")
print("   - Press ESC to exit")

try:
    while True:
        ret, frame = cap.read()

        if not ret:
            print("❌ Error: Could not read frame from camera.")
            break

        frame = cv2.flip(frame, 1)
        gray = cv2.cvtColor(frame, cv2.COLOR_BGR2GRAY)
        frame_count += 1

        # Detect faces
        faces = face_cascade.detectMultiScale(gray, 1.3, 5)

        if len(faces) == 0:
            # No face detected
            cv2.putText(frame, 'No face detected - Please face the camera', (10, 40),
                       cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 0, 255), 2)
        else:
            # Process the largest face (closest to camera)
            face_areas = [(w * h, (x, y, w, h)) for (x, y, w, h) in faces]
            _, (x, y, w, h) = max(face_areas)

            # Draw face rectangle
            cv2.rectangle(frame, (x, y), (x+w, y+h), (255, 0, 0), 2)

            # Extract face region
            roi_gray = gray[y:y+h, x:x+w]

            # Detect mouth/smile region
            mouths = mouth_cascade.detectMultiScale(roi_gray, 1.5, 11)

            mouth_detected = False
            for (mx, my, mw, mh) in mouths:
                # Filter out false positives (mouth should be in lower half of face)
                if my > h // 2 and mw > 20 and mh > 10:  # Minimum size validation
                    mouth_img = roi_gray[my:my+mh, mx:mx+mw]

                    # Resize for consistent processing
                    try:
                        mouth_img = cv2.resize(mouth_img, (100, 50))
                    except cv2.error:
                        continue  # Skip if resize fails

                    # Motion detection
                    motion_score = detect_speech_motion(prev_frame, mouth_img)

                    # Speech detection logic
                    if motion_score > motion_threshold_high and not speech_started:
                        speech_started = True
                        start_time = time.time()
                        print(f"🟢 Speaking detected... (motion: {motion_score:.1f})")

                    elif motion_score < motion_threshold_low and speech_started:
                        duration = time.time() - start_time
                        speech_started = False

                        predicted_word = predict_word(duration)
                        print(f"📝 Predicted: {predicted_word} (duration: {duration:.1f}s)")

                    # Update previous frame
                    prev_frame = mouth_img.copy()

                    # Draw mouth rectangle
                    cv2.rectangle(frame, (x+mx, y+my), (x+mx+mw, y+my+mh), (0, 255, 0), 2)

                    # Display motion score for debugging
                    cv2.putText(frame, f'Motion: {motion_score:.1f}', (x+mx, y+my-10),
                               cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 255, 0), 1)

                    mouth_detected = True
                    break

            if not mouth_detected:
                cv2.putText(frame, 'Mouth not detected - Speak more clearly', (10, 70),
                           cv2.FONT_HERSHEY_SIMPLEX, 0.6, (0, 165, 255), 2)

        # Status display
        status_text = "🟢 Speaking..." if speech_started else "Speak Silently..."
        cv2.putText(frame, status_text, (10, 40),
                   cv2.FONT_HERSHEY_SIMPLEX, 1, (255, 255, 255), 2)

        # Instructions
        cv2.putText(frame, 'Press ESC to exit', (10, frame.shape[0] - 20),
                   cv2.FONT_HERSHEY_SIMPLEX, 0.6, (255, 255, 255), 1)

        cv2.imshow('SILEXA - Silent Speech Recognition', frame)

        # Exit on ESC key
        key = cv2.waitKey(1) & 0xFF
        if key == 27:  # ESC key
            break
        elif key == ord('r'):  # Reset on 'r' key
            prev_frame = None
            speech_started = False
            print("🔄 Reset detection")

except KeyboardInterrupt:
    print("\n🛑 Interrupted by user")
except Exception as e:
    print(f"❌ An error occurred: {e}")
finally:
    # Cleanup
    print("🧹 Cleaning up...")
    cap.release()
    cv2.destroyAllWindows()
    print("✅ Cleanup complete. Goodbye!")
