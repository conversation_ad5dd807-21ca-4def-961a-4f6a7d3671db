import cv2
import mediapipe as mp

# Initialize MediaPipe Hands
mp_hands = mp.solutions.hands
hands = mp_hands.Hands(max_num_hands=1)
mp_draw = mp.solutions.drawing_utils

# Webcam setup
cap = cv2.VideoCapture(0)

# Finger tip IDs (thumb to pinky)
tip_ids = [4, 8, 12, 16, 20]

# Function to count raised fingers
def count_fingers(hand_landmarks):
    fingers = []

    # Thumb (check x-coordinates for left/right hand)
    if hand_landmarks.landmark[tip_ids[0]].x < hand_landmarks.landmark[tip_ids[0] - 1].x:
        fingers.append(1)
    else:
        fingers.append(0)

    # Other 4 fingers
    for id in range(1, 5):
        if hand_landmarks.landmark[tip_ids[id]].y < hand_landmarks.landmark[tip_ids[id] - 2].y:
            fingers.append(1)
        else:
            fingers.append(0)

    return fingers

# Sign mapping (example)
def get_sign(fingers):
    if fingers == [0, 1, 0, 0, 0]:
        return "A"
    elif fingers == [0, 1, 1, 0, 0]:
        return "B"
    elif fingers == [0, 1, 1, 1, 0]:
        return "C"
    elif fingers == [0, 1, 1, 1, 1]:
        return "D"
    elif fingers == [1, 1, 1, 1, 1]:
        return "Open Palm"
    elif fingers == [0, 0, 0, 0, 0]:
        return "Fist"
    else:
        return "Unknown"

# Main loop
while True:
    success, img = cap.read()
    img_rgb = cv2.cvtColor(img, cv2.COLOR_BGR2RGB)
    results = hands.process(img_rgb)

    if results.multi_hand_landmarks:
        for handLms in results.multi_hand_landmarks:
            mp_draw.draw_landmarks(img, handLms, mp_hands.HAND_CONNECTIONS)

            finger_states = count_fingers(handLms)
            sign = get_sign(finger_states)

            cv2.putText(img, f'Sign: {sign}', (10, 70),
                        cv2.FONT_HERSHEY_SIMPLEX, 1, (0, 255, 0), 2)

    cv2.imshow("Sign Language to Text", img)
    if cv2.waitKey(1) & 0xFF == 27:  # ESC to exit
        break

cap.release()
cv2.destroyAllWindows()
