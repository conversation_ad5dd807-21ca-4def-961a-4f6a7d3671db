# SILEXA - Silent Speech Recognition

A computer vision-based system that detects silent speech (lip movements) and predicts words based on motion patterns and duration.

## 🚀 Features

- **Real-time face detection** using Haar cascades
- **Mouth/lip movement detection** using smile cascade classifier
- **Motion-based speech detection** with adaptive thresholds
- **Duration-based word prediction** for common words
- **Robust error handling** and user feedback
- **Interactive controls** (ESC to exit, R to reset)

## 🔧 Fixed Issues

The following issues were identified and fixed in the original code:

### 1. **Error Handling**
- ✅ Added camera initialization validation
- ✅ Added cascade loading verification
- ✅ Added frame reading error handling
- ✅ Added graceful exit on errors

### 2. **Motion Detection Improvements**
- ✅ Normalized motion scores for consistent thresholds
- ✅ Added adaptive motion thresholds
- ✅ Improved false positive filtering
- ✅ Added minimum mouth size validation

### 3. **User Experience**
- ✅ Added detailed status messages
- ✅ Added face detection feedback
- ✅ Added mouth detection feedback
- ✅ Added motion score display for debugging
- ✅ Added reset functionality (R key)

### 4. **Code Structure**
- ✅ Modularized functions for better maintainability
- ✅ Added comprehensive error handling
- ✅ Added proper cleanup on exit
- ✅ Added keyboard interrupt handling

### 5. **Robustness**
- ✅ Handle cases with no face detected
- ✅ Handle cases with no mouth detected
- ✅ Validate frame dimensions before processing
- ✅ Added try-catch blocks for OpenCV operations

## 📋 Requirements

- Python 3.6+
- OpenCV (`cv2`)
- NumPy
- Working webcam

## 🏃‍♂️ Usage

### Run the main application:
```bash
python silexa.py
```

### Run tests to verify installation:
```bash
python test_silexa.py
```

## 🎮 Controls

- **ESC**: Exit the application
- **R**: Reset detection state
- **Camera**: Look directly at the camera and make silent mouth movements

## 📝 Word Predictions

The system predicts words based on speech duration:

| Duration | Predicted Word |
|----------|----------------|
| < 0.5s   | [Too Short]    |
| 0.5-2s   | Yes            |
| 2-3s     | No             |
| 3-4s     | Hello          |
| 4-5s     | Help           |
| 5-6s     | Go             |
| 6s+      | Stop           |

## 🔍 Troubleshooting

### Camera Issues
- Ensure your webcam is connected and not being used by another application
- Try running `test_silexa.py` to verify camera functionality

### Detection Issues
- Ensure good lighting conditions
- Face the camera directly
- Make clear mouth movements
- Adjust motion thresholds if needed

### Performance Issues
- Close other applications using the camera
- Ensure sufficient CPU resources
- Consider reducing frame resolution if needed

## 🧪 Testing

The `test_silexa.py` script verifies:
- OpenCV installation
- Cascade classifier loading
- Motion detection algorithm
- Word prediction logic
- Camera availability

## 📊 Technical Details

### Motion Detection Algorithm
1. Captures mouth region from face detection
2. Calculates frame difference between consecutive mouth images
3. Normalizes motion score by frame size
4. Uses adaptive thresholds for speech start/stop detection

### Face Detection
- Uses Haar cascade classifier for frontal faces
- Processes the largest detected face (closest to camera)
- Filters mouth detections to lower half of face

### Word Prediction
- Based on duration of detected mouth movement
- Simple time-based classification
- Can be extended with more sophisticated ML models

## 🔮 Future Improvements

- Machine learning-based lip reading
- Support for more words/phrases
- Real-time audio-visual correlation
- Multi-language support
- Improved accuracy with deep learning models

## 📄 License

This project is part of a hackathon submission for silent speech recognition technology.
