#!/usr/bin/env python3
"""
Test script for SILEXA components
Tests the functionality without requiring a camera
"""

import cv2
import numpy as np
import sys
import os

def test_opencv_installation():
    """Test if OpenCV is properly installed"""
    print("🔍 Testing OpenCV installation...")
    try:
        print(f"✅ OpenCV version: {cv2.__version__}")
        return True
    except Exception as e:
        print(f"❌ OpenCV test failed: {e}")
        return False

def test_cascade_loading():
    """Test if cascade classifiers can be loaded"""
    print("\n🔍 Testing cascade classifiers...")
    
    try:
        # Test face cascade
        face_cascade_path = cv2.data.haarcascades + 'haarcascade_frontalface_default.xml'
        face_cascade = cv2.CascadeClassifier(face_cascade_path)
        
        if face_cascade.empty():
            print("❌ Face cascade failed to load")
            return False
        else:
            print("✅ Face cascade loaded successfully")
        
        # Test smile cascade (used as mouth detector)
        smile_cascade_path = cv2.data.haarcascades + 'haarcascade_smile.xml'
        smile_cascade = cv2.CascadeClassifier(smile_cascade_path)
        
        if smile_cascade.empty():
            print("❌ Smile cascade failed to load")
            return False
        else:
            print("✅ Smile cascade loaded successfully")
            
        return True
        
    except Exception as e:
        print(f"❌ Cascade loading test failed: {e}")
        return False

def test_motion_detection():
    """Test motion detection algorithm with dummy data"""
    print("\n🔍 Testing motion detection algorithm...")
    
    try:
        # Create dummy frames
        frame1 = np.random.randint(0, 255, (50, 100), dtype=np.uint8)
        frame2 = np.random.randint(0, 255, (50, 100), dtype=np.uint8)
        frame3 = frame1.copy()  # Identical frame
        
        # Test motion detection function from silexa.py
        def detect_speech_motion(prev_frame, current_frame):
            if prev_frame is None or current_frame is None:
                return 0
            
            diff = cv2.absdiff(prev_frame, current_frame)
            motion_score = np.sum(diff)
            normalized_score = motion_score / (current_frame.shape[0] * current_frame.shape[1])
            return normalized_score
        
        # Test with different frames (should detect motion)
        motion1 = detect_speech_motion(frame1, frame2)
        print(f"✅ Motion between different frames: {motion1:.2f}")
        
        # Test with identical frames (should detect no motion)
        motion2 = detect_speech_motion(frame1, frame3)
        print(f"✅ Motion between identical frames: {motion2:.2f}")
        
        # Test with None frame
        motion3 = detect_speech_motion(None, frame1)
        print(f"✅ Motion with None frame: {motion3:.2f}")
        
        return True
        
    except Exception as e:
        print(f"❌ Motion detection test failed: {e}")
        return False

def test_word_prediction():
    """Test word prediction based on duration"""
    print("\n🔍 Testing word prediction...")
    
    try:
        def predict_word(duration):
            if duration < 0.5:
                return "[Too Short]"
            elif 0.5 <= duration < 2:
                return "Yes"
            elif 2 <= duration < 3:
                return "No"
            elif 3 <= duration < 4:
                return "Hello"
            elif 4 <= duration < 5:
                return "Help"
            elif 5 <= duration < 6:
                return "Go"
            elif duration >= 6:
                return "Stop"
            else:
                return "[Unclear]"
        
        test_cases = [0.3, 1.0, 2.5, 3.5, 4.5, 5.5, 7.0]
        expected = ["[Too Short]", "Yes", "No", "Hello", "Help", "Go", "Stop"]
        
        for duration, expected_word in zip(test_cases, expected):
            predicted = predict_word(duration)
            if predicted == expected_word:
                print(f"✅ Duration {duration}s -> {predicted}")
            else:
                print(f"❌ Duration {duration}s -> {predicted} (expected {expected_word})")
                return False
                
        return True
        
    except Exception as e:
        print(f"❌ Word prediction test failed: {e}")
        return False

def test_camera_availability():
    """Test if camera is available (non-blocking)"""
    print("\n🔍 Testing camera availability...")
    
    try:
        cap = cv2.VideoCapture(0)
        
        if not cap.isOpened():
            print("⚠️  Camera not available (this is OK for testing)")
            return True
        
        ret, frame = cap.read()
        if ret:
            print(f"✅ Camera working - Frame shape: {frame.shape}")
        else:
            print("⚠️  Camera opened but couldn't read frame")
            
        cap.release()
        return True
        
    except Exception as e:
        print(f"⚠️  Camera test failed: {e} (this is OK for testing)")
        return True

def main():
    """Run all tests"""
    print("🧪 SILEXA Component Tests")
    print("=" * 50)
    
    tests = [
        ("OpenCV Installation", test_opencv_installation),
        ("Cascade Loading", test_cascade_loading),
        ("Motion Detection", test_motion_detection),
        ("Word Prediction", test_word_prediction),
        ("Camera Availability", test_camera_availability),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n{'='*20} {test_name} {'='*20}")
        if test_func():
            passed += 1
            print(f"✅ {test_name} PASSED")
        else:
            print(f"❌ {test_name} FAILED")
    
    print(f"\n{'='*50}")
    print(f"📊 Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! SILEXA is ready to use.")
        return 0
    else:
        print("⚠️  Some tests failed. Please check the issues above.")
        return 1

if __name__ == "__main__":
    sys.exit(main())
